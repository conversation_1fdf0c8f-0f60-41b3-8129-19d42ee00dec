"use client";

import { useEffect, useState, useRef, ReactNode } from "react";

// Intersection Observer hook for scroll animations
export function useIntersectionObserver(
  options = { threshold: 0.1, triggerOnce: true }
) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          if (options.triggerOnce && ref.current) {
            observer.unobserve(ref.current);
          }
        } else if (!options.triggerOnce) {
          setIsVisible(false);
        }
      },
      {
        threshold: options.threshold,
        rootMargin: "0px",
      }
    );

    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [options.threshold, options.triggerOnce]);

  return { ref, isVisible };
}

// Animated element component
export function AnimatedElement({
  children,
  animation = "fade-in",
  delay = 0,
  duration = 1000,
  threshold = 0.1,
  className = "",
}: {
  children: ReactNode;
  animation?:
    | "fade-in"
    | "slide-up"
    | "slide-left"
    | "slide-right"
    | "zoom-in"
    | "bounce";
  delay?: number;
  duration?: number;
  threshold?: number;
  className?: string;
}) {
  const { ref, isVisible } = useIntersectionObserver({
    threshold,
    triggerOnce: true,
  });

  const getAnimationClass = () => {
    switch (animation) {
      case "fade-in":
        return "opacity-0";
      case "slide-up":
        return "opacity-0 translate-y-10";
      case "slide-left":
        return "opacity-0 -translate-x-10";
      case "slide-right":
        return "opacity-0 translate-x-10";
      case "zoom-in":
        return "opacity-0 scale-95";
      case "bounce":
        return "opacity-0";
      default:
        return "opacity-0";
    }
  };

  const getAnimationStyle = () => {
    if (!isVisible) return {};

    const baseStyle = {
      opacity: 1,
      transform: "translate(0, 0) scale(1)",
      transition: `all ${duration}ms cubic-bezier(0.4, 0, 0.2, 1) ${delay}ms`,
    };

    if (animation === "bounce" && isVisible) {
      return {
        ...baseStyle,
        animation: `bounce 1s ${delay}ms cubic-bezier(0.4, 0, 0.2, 1)`,
      };
    }

    return baseStyle;
  };

  return (
    <div
      ref={ref}
      className={`${className} ${getAnimationClass()}`}
      style={getAnimationStyle()}
    >
      {children}
    </div>
  );
}

// Scroll progress indicator
export function ScrollProgress() {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const totalHeight = document.body.scrollHeight - window.innerHeight;
      const progress = (window.scrollY / totalHeight) * 100;
      setScrollProgress(progress);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="fixed top-0 left-0 w-full h-1 z-50">
      <div
        className="h-full bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500"
        style={{ width: `${scrollProgress}%` }}
      ></div>
    </div>
  );
}

// Particle interface for type safety
interface Particle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  color: string;
  opacity: number;
  life: number;
  maxLife: number;
  init(): void;
  update(): void;
  draw(): void;
  isDead(): boolean;
}

// Performance-optimized particle background component
export function ParticleBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const particlesRef = useRef<Particle[]>([]);
  const lastTimeRef = useRef<number>(0);
  const fpsRef = useRef<number>(60);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d', { alpha: false });
    if (!ctx) return;

    // Performance settings based on device capabilities
    const getPerformanceSettings = () => {
      const isLowEnd = navigator.hardwareConcurrency <= 4 ||
                      /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

      if (prefersReducedMotion) {
        return { particleCount: 0, targetFPS: 0 };
      }

      return {
        particleCount: isLowEnd ? 30 : 60,
        targetFPS: isLowEnd ? 30 : 60,
        enableConnections: !isLowEnd,
      };
    };

    const settings = getPerformanceSettings();
    if (settings.particleCount === 0) return; // Skip if reduced motion

    // Set canvas dimensions with device pixel ratio for crisp rendering
    const setCanvasDimensions = () => {
      if (!canvas) return;
      const dpr = Math.min(window.devicePixelRatio || 1, 2); // Cap at 2x for performance
      const rect = canvas.getBoundingClientRect();

      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;
      canvas.style.width = rect.width + 'px';
      canvas.style.height = rect.height + 'px';

      ctx.scale(dpr, dpr);
    };

    setCanvasDimensions();

    // Throttled resize handler
    let resizeTimeout: NodeJS.Timeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(setCanvasDimensions, 100);
    };
    window.addEventListener('resize', handleResize);

    // Get theme-aware colors from CSS variables with caching
    let cachedColors: string[] | null = null;
    const getThemeColors = () => {
      if (cachedColors) return cachedColors;

      const root = document.documentElement;
      cachedColors = [
        getComputedStyle(root).getPropertyValue('--particle-color-1').trim() || 'rgba(59, 130, 246, 0.8)',
        getComputedStyle(root).getPropertyValue('--particle-color-2').trim() || 'rgba(96, 165, 250, 0.6)',
        getComputedStyle(root).getPropertyValue('--particle-color-3').trim() || 'rgba(147, 197, 253, 0.4)',
      ];
      return cachedColors;
    };

    // Optimized Particle class with object pooling
    class Particle {
      x: number = 0;
      y: number = 0;
      size: number = 0;
      speedX: number = 0;
      speedY: number = 0;
      color: string = '';
      opacity: number = 1;
      life: number = 1;
      maxLife: number = 1;

      init() {
        if (!canvas) return;
        const rect = canvas.getBoundingClientRect();
        this.x = Math.random() * rect.width;
        this.y = Math.random() * rect.height;
        this.size = Math.random() * 2 + 0.5; // Smaller particles for better performance
        this.speedX = (Math.random() - 0.5) * 0.3; // Slower movement
        this.speedY = (Math.random() - 0.5) * 0.3;
        this.life = this.maxLife = Math.random() * 300 + 200; // Particle lifespan
        this.opacity = Math.random() * 0.5 + 0.3;

        // Use theme-aware colors
        const themeColors = getThemeColors();
        this.color = themeColors[Math.floor(Math.random() * themeColors.length)];
      }

      update() {
        if (!canvas) return;
        const rect = canvas.getBoundingClientRect();

        this.x += this.speedX;
        this.y += this.speedY;
        this.life--;

        // Wrap around screen edges
        if (this.x > rect.width) this.x = 0;
        else if (this.x < 0) this.x = rect.width;
        if (this.y > rect.height) this.y = 0;
        else if (this.y < 0) this.y = rect.height;

        // Fade out over time
        this.opacity = (this.life / this.maxLife) * 0.8;
      }

      draw() {
        if (!ctx || this.life <= 0) return;

        ctx.save();
        ctx.globalAlpha = this.opacity;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
      }

      isDead() {
        return this.life <= 0;
      }
    }

    // Initialize particle pool
    const particles: Particle[] = [];
    for (let i = 0; i < settings.particleCount; i++) {
      const particle = new Particle();
      particle.init();
      particles.push(particle);
    }
    particlesRef.current = particles;

    // Function to update particle colors when theme changes
    const updateParticleColors = () => {
      cachedColors = null; // Clear cache
      const themeColors = getThemeColors();
      particles.forEach(particle => {
        particle.color = themeColors[Math.floor(Math.random() * themeColors.length)];
      });
    };

    // Listen for theme changes with debouncing
    let themeUpdateTimeout: NodeJS.Timeout;
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          clearTimeout(themeUpdateTimeout);
          themeUpdateTimeout = setTimeout(updateParticleColors, 100);
        }
      });
    });

    observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });

    // FPS-controlled animation loop
    const targetFrameTime = 1000 / settings.targetFPS;

    const animate = (currentTime: number) => {
      if (!ctx || !canvas) return;

      const deltaTime = currentTime - lastTimeRef.current;

      if (deltaTime >= targetFrameTime) {
        // Clear canvas with background color for better performance
        ctx.fillStyle = 'rgba(10, 10, 15, 0.1)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Update and draw particles
        for (let i = particles.length - 1; i >= 0; i--) {
          const particle = particles[i];
          particle.update();

          if (particle.isDead()) {
            // Respawn particle instead of creating new ones
            particle.init();
          }

          particle.draw();
        }

        lastTimeRef.current = currentTime;

        // Calculate actual FPS for monitoring
        fpsRef.current = 1000 / deltaTime;
      }

      animationFrameRef.current = requestAnimationFrame(animate);
    };

    // Start animation
    animationFrameRef.current = requestAnimationFrame(animate);

    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleResize);
      observer.disconnect();
      clearTimeout(resizeTimeout);
      clearTimeout(themeUpdateTimeout);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="fixed top-0 left-0 w-full h-full pointer-events-none z-0"
      aria-hidden="true"
    />
  );
}

// Card 3D Effect Component
export function Card3D({
  children,
  className = "",
  intensity = 5
}: {
  children: ReactNode;
  className?: string;
  intensity?: number;
}) {
  const [transform, setTransform] = useState("");
  const cardRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;

    const card = cardRef.current;
    const rect = card.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    const rotateX = (y - centerY) / intensity;
    const rotateY = (centerX - x) / intensity;

    setTransform(`perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`);
  };

  const handleMouseLeave = () => {
    setTransform("");
  };

  return (
    <div
      ref={cardRef}
      className={`transition-transform duration-300 ease-out ${className}`}
      style={{ transform }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      {children}
    </div>
  );
}

// Lightning Effect Component
export function LightningBackground() {
  return (
    <>
      {/* Dynamic Background Overlay */}
      <div className="dynamic-bg-overlay"></div>

      {/* Lightning Bolts */}
      <div className="lightning-bolt lightning-bolt-1"></div>
      <div className="lightning-bolt lightning-bolt-2"></div>
      <div className="lightning-bolt lightning-bolt-3"></div>
      <div className="lightning-bolt lightning-bolt-4"></div>
      <div className="lightning-bolt lightning-bolt-5"></div>

      {/* Additional Random Lightning Effects */}
      <div
        className="lightning-bolt"
        style={{
          top: '45%',
          left: '25%',
          animation: 'randomLightning3 18s infinite',
          animationDelay: '15s',
          height: '80px'
        }}
      ></div>
      <div
        className="lightning-bolt"
        style={{
          top: '70%',
          right: '15%',
          animation: 'randomLightning1 16s infinite',
          animationDelay: '18s',
          height: '120px'
        }}
      ></div>
      <div
        className="lightning-bolt"
        style={{
          top: '15%',
          left: '80%',
          animation: 'randomLightning2 20s infinite',
          animationDelay: '21s',
          height: '90px'
        }}
      ></div>
    </>
  );
}

// Dynamic Lighting Effects Component
export function DynamicLighting() {
  const [lights, setLights] = useState<Array<{
    id: number;
    x: number;
    y: number;
    color: string;
    size: number;
    opacity: number;
    duration: number;
    delay: number;
    isVisible: boolean;
    animationType: number;
  }>>([]);

  useEffect(() => {
    // Check if user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      return;
    }

    // Check device capabilities for performance optimization
    const isLowEnd = navigator.hardwareConcurrency <= 4 ||
                    /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    const maxLights = isLowEnd ? 3 : 6;
    const baseInterval = isLowEnd ? 4000 : 2500;

    const lightColors = [
      'rgba(59, 130, 246, 0.6)',   // Blue
      'rgba(139, 92, 246, 0.6)',   // Purple
      'rgba(6, 182, 212, 0.6)',    // Cyan
      'rgba(255, 255, 255, 0.4)',  // White
      'rgba(251, 191, 36, 0.6)',   // Warm Yellow
    ];

    const createLight = () => {
      const newLight = {
        id: Date.now() + Math.random(),
        x: Math.random() * 100, // Percentage
        y: Math.random() * 100, // Percentage
        color: lightColors[Math.floor(Math.random() * lightColors.length)],
        size: Math.random() * 200 + 100, // 100-300px
        opacity: Math.random() * 0.4 + 0.2, // 0.2-0.6
        duration: Math.random() * 2000 + 3000, // 3-5 seconds
        delay: Math.random() * 1000, // 0-1 second delay
        isVisible: false,
        animationType: Math.floor(Math.random() * 3), // 0: pulse, 1: drift, 2: glow
      };

      setLights(prevLights => {
        // Remove old lights if we have too many
        const filteredLights = prevLights.slice(-maxLights + 1);
        return [...filteredLights, newLight];
      });

      // Make light visible after delay
      setTimeout(() => {
        setLights(prevLights =>
          prevLights.map(light =>
            light.id === newLight.id ? { ...light, isVisible: true } : light
          )
        );
      }, newLight.delay);

      // Remove light after duration
      setTimeout(() => {
        setLights(prevLights =>
          prevLights.filter(light => light.id !== newLight.id)
        );
      }, newLight.duration + newLight.delay);
    };

    // Create initial lights with staggered timing
    const initialDelays = [0, 800, 1600, 2400, 3200, 4000];
    initialDelays.slice(0, maxLights).forEach((delay) => {
      setTimeout(createLight, delay);
    });

    // Set up interval for continuous light creation
    const interval = setInterval(createLight, baseInterval + Math.random() * 1000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  if (lights.length === 0) {
    return null;
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-0" aria-hidden="true">
      {lights.map((light) => (
        <div
          key={light.id}
          className={`absolute rounded-full transition-all duration-1000 ease-in-out ${
            light.animationType === 0 ? 'dynamic-light-pulse' :
            light.animationType === 1 ? 'dynamic-light-drift' : 'dynamic-light-glow'
          } ${light.isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-50'}`}
          style={{
            left: `${light.x}%`,
            top: `${light.y}%`,
            width: `${light.size}px`,
            height: `${light.size}px`,
            background: `radial-gradient(circle, ${light.color} 0%, transparent 70%)`,
            filter: 'blur(20px)',
            transform: 'translate(-50%, -50%)',
            opacity: light.isVisible ? light.opacity : 0,
          }}
        />
      ))}
    </div>
  );
}
