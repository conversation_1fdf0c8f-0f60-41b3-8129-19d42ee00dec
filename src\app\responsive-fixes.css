/* Mobile and Small Screen Responsive Fixes */

/* Base responsive fixes */
html, body {
  overflow-x: hidden; /* Prevent horizontal scrolling */
  touch-action: manipulation; /* Optimize touch actions */
  scroll-behavior: smooth; /* Modern smooth scrolling for all browsers */
}

img {
  max-width: 100%; /* Make all images responsive by default */
  height: auto; /* Maintain aspect ratio */
}

/* Better responsive grid system with more control */
@media (max-width: 768px) {
  .grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)) !important;
  }
}

/* General responsiveness improvements */
@media (max-width: 640px) {
  /* Fix container padding on small screens */
  .container {
    padding-left: 16px !important;
    padding-right: 16px !important;
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* Adjust logo size on very small screens */
  .text-gradient-enhanced {
    font-size: 1rem !important;
  }
  
  /* Reduce padding in cards on small screens */
  .p-6 {
    padding: 1rem !important;
  }
  
  /* Adjust gaps in grids */
  .gap-8 {
    gap: 1rem !important;
  }
  
  /* Fix padding on mobile */
  .py-8 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  
  /* Better text sizing for mobile */
  .text-xl {
    font-size: 1.125rem !important;
  }
  
  /* Improve mobile menu positioning */
  .mobile-menu-container {
    width: 85% !important;
    max-width: 300px;
  }
  
  /* Improve navbar on mobile */
  nav {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
}

/* Tablet-specific adjustments */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Optimize container width for tablets */
  .container {
    max-width: 95% !important;
  }
  
  /* Ensure proper card sizing on tablets */
  .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }
  
  /* Better padding for content on tablets */
  .px-6 {
    padding-left: 1.25rem !important;
    padding-right: 1.25rem !important;
  }
}

/* Fix for very small screens */
@media (max-width: 360px) {
  /* Better padding for tiny screens */
  .container {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }
  
  /* Smaller icons on tiny screens */
  .h-16 {
    height: 3rem !important;
  }
  
  .w-16 {
    width: 3rem !important;
  }
  
  /* Adjust card heights for tiny screens */
  .h-48, .h-40, .h-36 {
    height: 10rem !important;
  }
  
  /* Adjust technology tag spacing */
  .gap-2, .gap-1 {
    gap: 0.375rem !important;
  }
  
  /* Make sure text is readable */
  .text-sm, .text-xs {
    font-size: 0.75rem !important;
  }
  
  /* Improve navbar logo on tiny screens */
  .text-lg {
    font-size: 0.875rem !important;
  }
}

/* Navbar responsive fixes */
.desktop-nav-container {
  display: none;
}

.mobile-menu-button-container {
  display: flex;
}

.nav-placeholder {
  width: 0;
}

.team-text {
  display: none;
}

@media (min-width: 768px) {
  .desktop-nav-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .mobile-menu-button-container {
    display: none;
  }
  
  .nav-placeholder {
    width: 200px;
  }
}

@media (min-width: 480px) {
  .team-text {
    display: inline;
  }
}

/* Better touch target sizes for mobile */
@media (hover: none) {
  /* Improve tap targets */
  button, a, .btn, [role="button"] {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem !important; /* Ensure adequate padding for touch targets */
  }
  
  /* Add touch feedback effect for mobile */
  a:active, button:active, .btn:active, [role="button"]:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
  
  /* Add active state styles for touch devices */
  .group:active .group-hover\:scale-105 {
    transform: scale(1.05);
  }
  
  .group:active .group-hover\:opacity-85 {
    opacity: 0.85;
  }
  
  .group:active .group-hover\:animate-bounce {
    animation: bounce 1s infinite;
  }
  
  /* Show mobile navigation more clearly on touch */
  .nav-link:active {
    background-color: rgba(59, 130, 246, 0.2) !important;
  }
}

/* Fix for sticky positioning on iOS */
@supports (-webkit-touch-callout: none) {
  .sticky {
    position: -webkit-sticky; /* iOS Safari */
    position: sticky; /* Standard position */
  }
  
  /* Fix iOS input zoom issue */
  input, select, textarea {
    font-size: 16px !important; /* Prevents iOS from zooming on input focus */
  }
}

/* Fix browser specific issues */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  select, textarea, input[type="text"], input[type="password"],
  input[type="datetime"], input[type="datetime-local"],
  input[type="date"], input[type="month"], input[type="time"],
  input[type="week"], input[type="number"], input[type="email"],
  input[type="url"], input[type="search"], input[type="tel"],
  input[type="color"] {
    font-size: 16px; /* Ensures no auto-zoom on iOS */
  }
}

/* Fix landscape orientation issues */
@media (max-height: 500px) and (orientation: landscape) {
  .mobile-menu-container {
    overflow-y: auto;
  }
  
  .h-48 {
    height: 8rem !important;
  }
}
