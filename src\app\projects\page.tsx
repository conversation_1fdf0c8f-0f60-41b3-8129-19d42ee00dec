"use client"

import { useState, useEffect } from "react";

import { AnimatedElement } from "@/utils/animations";
import { LazyAnimationLoader, OptimizedScrollProgress } from "@/components/LazyAnimations";

import Card3D from "@/components/Card3D";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import ProjectsGrid from "@/components/ProjectsGrid";

export default function Projects() {
  const [scrollPosition, setScrollPosition] = useState(0);

  // Handle scroll position for animations
  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(window.scrollY);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="min-h-screen text-white transition-colors duration-500">
      {/* Optimized Background Effects */}
      <LazyAnimationLoader
        enableParticles={true}
        enableLightning={false}
        enableDynamicLighting={true}
        priority="medium"
      />

      {/* Optimized Scroll Progress Indicator */}
      <OptimizedScrollProgress />

      {/* Back to Top Button - appears when scrolling down */}
      {scrollPosition > 500 && (
        <button
          type="button"
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="fixed bottom-8 right-8 z-50 p-3 rounded-full bg-blue-500 hover:bg-blue-600 shadow-lg animate-fade-in hover-scale"
          aria-label="Back to top"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        </button>
      )}

      {/* Navigation */}
      <Navbar activeSection="projects" />

      {/* Projects Header Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-40 left-10 w-60 h-60 rounded-full bg-blue-500/5 animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 rounded-full bg-indigo-500/5 animate-float delay-300"></div>

        <div className="container mx-auto px-6 relative">
          <AnimatedElement animation="fade-in">
            <h1 className="text-5xl md:text-6xl font-bold mb-8 text-center">
              <span className="text-gradient">Our Projects</span>
              <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mt-6"></div>
            </h1>
            <p className="text-xl max-w-3xl mx-auto text-center mb-8 opacity-90">
              Explore our innovative solutions built during hackathons and beyond. Each project demonstrates our team&apos;s technical skills and creative problem-solving abilities.
            </p>

            {/* Project Categories */}
            <div className="flex flex-wrap justify-center gap-3 mb-16">
              <button className="glass-effect px-5 py-2 rounded-full text-blue-300 hover:text-white hover:bg-blue-500/30 transition-all duration-300 border border-blue-400/30 hover:border-blue-400/50 transform hover:-translate-y-1 active:scale-95">
                All Projects
              </button>
              <button className="glass-effect px-5 py-2 rounded-full text-blue-300 hover:text-white hover:bg-blue-500/30 transition-all duration-300 border border-blue-400/30 hover:border-blue-400/50 transform hover:-translate-y-1 active:scale-95">
                Web Apps
              </button>
              <button className="glass-effect px-5 py-2 rounded-full text-blue-300 hover:text-white hover:bg-blue-500/30 transition-all duration-300 border border-blue-400/30 hover:border-blue-400/50 transform hover:-translate-y-1 active:scale-95">
                Mobile
              </button>
              <button className="glass-effect px-5 py-2 rounded-full text-blue-300 hover:text-white hover:bg-blue-500/30 transition-all duration-300 border border-blue-400/30 hover:border-blue-400/50 transform hover:-translate-y-1 active:scale-95">
                AI/ML
              </button>
              <button className="glass-effect px-5 py-2 rounded-full text-blue-300 hover:text-white hover:bg-blue-500/30 transition-all duration-300 border border-blue-400/30 hover:border-blue-400/50 transform hover:-translate-y-1 active:scale-95">
                IoT
              </button>
            </div>
          </AnimatedElement>
        </div>
      </section>

      {/* Featured Project Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="container mx-auto px-6 relative">
          {/* Decorative elements */}
          <div className="absolute top-20 right-10 w-40 h-40 rounded-full bg-blue-500/5 animate-spin-slow"></div>
          <div className="absolute bottom-20 left-10 w-60 h-60 rounded-full bg-indigo-500/5 animate-spin-slow"></div>

          <AnimatedElement animation="fade-in">
            <h2 className="text-3xl font-bold mb-12 text-center">
              <span className="text-gradient">Featured Project</span>
              <div className="w-16 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mt-4"></div>
            </h2>
          </AnimatedElement>

          {/* Featured Project - SwiftDrop */}
          <AnimatedElement animation="slide-up" delay={200}>
            <Card3D className="max-w-5xl mx-auto mb-20 glass-effect rounded-xl overflow-hidden shadow-2xl shadow-blue-500/10 hover:shadow-blue-500/20 transition-all duration-500" intensity={15}>
              <div className="grid grid-cols-1 md:grid-cols-2">
                <div className="p-8 flex flex-col justify-center">
                  <div className="inline-block px-3 py-1 rounded-full bg-blue-500/20 text-blue-300 text-sm mb-4 animate-pulse-subtle">
                    Featured Project
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-gradient">SwiftDrop</h3>
                  <p className="text-lg mb-6">
                    A modern file sharing platform that allows users to upload and share files easily with anyone. Features include secure transfers, no account required to download, and a sleek user interface.
                  </p>
                  <div className="flex flex-wrap gap-2 mb-6">
                    <span className="glass-effect px-3 py-1 rounded-full text-sm hover:bg-blue-500/20 transition-colors cursor-pointer hover:scale-105 transform duration-300">React</span>
                    <span className="glass-effect px-3 py-1 rounded-full text-sm hover:bg-blue-500/20 transition-colors cursor-pointer hover:scale-105 transform duration-300">TypeScript</span>
                    <span className="glass-effect px-3 py-1 rounded-full text-sm hover:bg-blue-500/20 transition-colors cursor-pointer hover:scale-105 transform duration-300">Supabase</span>
                  </div>
                  <div className="flex gap-4">
                    <a
                      href="https://swiftdropio.netlify.app/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center bg-blue-500/20 hover:bg-blue-500/40 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 hover:-translate-y-1 active:translate-y-0"
                    >
                      Visit Project
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </a>
                    <a
                      href="#"
                      className="inline-flex items-center glass-effect hover:bg-white/10 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 hover:-translate-y-1 active:translate-y-0"
                    >
                      View Details
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </a>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-indigo-500/20 to-blue-500/20 p-8 flex items-center justify-center relative group overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 via-blue-500/10 to-indigo-500/0 opacity-0 group-hover:opacity-100 transform translate-x-full group-hover:translate-x-0 transition-all duration-1000"></div>
                  <div className="w-full h-64 bg-blue-800/30 rounded-lg flex items-center justify-center relative overflow-hidden group-hover:scale-105 transition-transform duration-500">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-20 w-20 text-blue-300/50 group-hover:text-blue-300/80 transition-colors duration-500 group-hover:scale-110 transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  </div>
                </div>
              </div>
            </Card3D>
          </AnimatedElement>
        </div>
      </section>

      {/* All Projects Grid */}
      <section className="py-16 bg-black/20 relative overflow-hidden">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-white">
            Our Projects
            <div className="w-16 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mt-4"></div>
          </h2>

          <ProjectsGrid />
        </div>
      </section>

      {/* Enhanced Footer */}
      <Footer variant="full" />
    </div>
  );
}