"use client";

import { useEffect, useState } from 'react';

interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  loadTime: number;
  isLowPerformance: boolean;
  connectionType: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
}

export function usePerformanceMetrics(): PerformanceMetrics {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    memoryUsage: 0,
    loadTime: 0,
    isLowPerformance: false,
    connectionType: 'unknown',
    deviceType: 'desktop'
  });

  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    // Detect device type
    const getDeviceType = (): 'desktop' | 'mobile' | 'tablet' => {
      const userAgent = navigator.userAgent;
      if (/tablet|ipad|playbook|silk/i.test(userAgent)) return 'tablet';
      if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) return 'mobile';
      return 'desktop';
    };

    // Get connection type
    const getConnectionType = (): string => {
      const nav = navigator as Navigator & {
        connection?: { effectiveType?: string };
        mozConnection?: { effectiveType?: string };
        webkitConnection?: { effectiveType?: string };
      };
      const connection = nav.connection || nav.mozConnection || nav.webkitConnection;
      return connection ? connection.effectiveType || 'unknown' : 'unknown';
    };

    // Measure page load time
    const getLoadTime = (): number => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      return navigation ? navigation.loadEventEnd - navigation.startTime : 0;
    };

    // FPS measurement
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));

        // Get memory usage if available
        const memory = (performance as Performance & { memory?: { usedJSHeapSize: number; jsHeapSizeLimit: number } }).memory;
        const memoryUsage = memory ? memory.usedJSHeapSize / memory.jsHeapSizeLimit : 0;

        const loadTime = getLoadTime();
        const connectionType = getConnectionType();
        const deviceType = getDeviceType();

        const isLowPerformance = fps < 30 || memoryUsage > 0.8 ||
                                connectionType === 'slow-2g' || connectionType === '2g' ||
                                (deviceType === 'mobile' && navigator.hardwareConcurrency <= 4);

        setMetrics({
          fps,
          memoryUsage,
          loadTime,
          isLowPerformance,
          connectionType,
          deviceType
        });

        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(measureFPS);
    };

    // Start measuring
    animationId = requestAnimationFrame(measureFPS);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  return metrics;
}

// Performance optimization recommendations
export function usePerformanceRecommendations() {
  const metrics = usePerformanceMetrics();

  const recommendations = [];

  if (metrics.fps < 30) {
    recommendations.push('Consider reducing animation complexity or particle count');
  }

  if (metrics.memoryUsage > 0.7) {
    recommendations.push('High memory usage detected - consider optimizing images and animations');
  }

  if (metrics.connectionType === 'slow-2g' || metrics.connectionType === '2g') {
    recommendations.push('Slow connection detected - consider loading minimal assets');
  }

  if (metrics.deviceType === 'mobile' && metrics.isLowPerformance) {
    recommendations.push('Low-end mobile device detected - enabling performance mode');
  }

  return {
    metrics,
    recommendations,
    shouldOptimize: metrics.isLowPerformance
  };
}

// Performance-aware component wrapper
interface PerformanceWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  threshold?: {
    fps?: number;
    memory?: number;
  };
}

export function PerformanceWrapper({
  children,
  fallback = null,
  threshold = { fps: 30, memory: 0.8 }
}: PerformanceWrapperProps) {
  const metrics = usePerformanceMetrics();

  const shouldShowFallback = metrics.fps < (threshold.fps || 30) ||
                            metrics.memoryUsage > (threshold.memory || 0.8);

  if (shouldShowFallback && fallback) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Development performance monitor (only shows in dev mode)
export function DevPerformanceMonitor() {
  const { metrics, recommendations } = usePerformanceRecommendations();
  const [isVisible, setIsVisible] = useState(false);

  // Only show in development
  useEffect(() => {
    setIsVisible(process.env.NODE_ENV === 'development');
  }, []);

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-black/80 text-white p-4 rounded-lg text-xs font-mono max-w-xs">
      <div className="mb-2 font-bold">Performance Monitor</div>
      <div>FPS: {metrics.fps}</div>
      <div>Memory: {(metrics.memoryUsage * 100).toFixed(1)}%</div>
      <div>Load Time: {metrics.loadTime.toFixed(0)}ms</div>
      <div>Connection: {metrics.connectionType}</div>
      <div>Device: {metrics.deviceType}</div>
      <div className={`mt-2 ${metrics.isLowPerformance ? 'text-red-400' : 'text-green-400'}`}>
        Status: {metrics.isLowPerformance ? 'Low Performance' : 'Good Performance'}
      </div>
      {recommendations.length > 0 && (
        <div className="mt-2">
          <div className="font-bold text-yellow-400">Recommendations:</div>
          {recommendations.map((rec, index) => (
            <div key={index} className="text-yellow-300 text-xs mt-1">• {rec}</div>
          ))}
        </div>
      )}
    </div>
  );
}

// Web Vitals monitoring
export function useWebVitals() {
  const [vitals, setVitals] = useState({
    CLS: 0,
    FID: 0,
    FCP: 0,
    LCP: 0,
    TTFB: 0
  });

  useEffect(() => {
    // This would typically use the web-vitals library
    // For now, we'll use basic performance API
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'largest-contentful-paint') {
          setVitals(prev => ({ ...prev, LCP: entry.startTime }));
        }
        if (entry.entryType === 'first-contentful-paint') {
          setVitals(prev => ({ ...prev, FCP: entry.startTime }));
        }
      }
    });

    observer.observe({ entryTypes: ['largest-contentful-paint', 'first-contentful-paint'] });

    return () => observer.disconnect();
  }, []);

  return vitals;
}

export default PerformanceWrapper;
