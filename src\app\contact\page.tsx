"use client"

import { useState, useEffect } from "react";
import { AnimatedElement } from "@/utils/animations";
import { LazyAnimationLoader, OptimizedScrollProgress } from "@/components/LazyAnimations";
import FAQ from "@/components/FAQ";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

export default function Contact() {
  const [scrollPosition, setScrollPosition] = useState(0);
  const activeSection = 'contact';

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(window.scrollY);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="min-h-screen text-white transition-colors duration-500">
      {/* Enhanced Background Effects */}
      <LazyAnimationLoader
        enableParticles={true}
        enableLightning={true}
        enableDynamicLighting={true}
        priority="high"
      />

      {/* Scroll Progress Indicator */}
      <OptimizedScrollProgress />

      {/* Back to Top Button - appears when scrolling down */}
      {scrollPosition > 500 && (
        <button
          type="button"
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="fixed bottom-8 right-8 z-50 p-4 rounded-2xl glass-effect-strong border border-white/20 shadow-lg animate-fade-in hover:scale-110 transition-all duration-300 group"
          aria-label="Back to top"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white group-hover:text-blue-300 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        </button>
      )}

      {/* Navigation */}
      <Navbar activeSection={activeSection} />

      {/* Contact Section */}
      <section className="min-h-screen pt-32 pb-20 relative">
        <div className="container mx-auto px-6 relative">

          <AnimatedElement animation="fade-in" duration={1000}>
            <h1 className="heading-lg font-bold mb-8 text-center">
              <span className="text-gradient-enhanced">Get in Touch</span>
            </h1>
            <p className="text-enhanced text-center max-w-4xl mx-auto mb-16">
              Have a question or want to work with us? We&apos;d love to hear from you. Fill out the form below or use our contact information to start a conversation.
            </p>
          </AnimatedElement>

          {/* Main content - Two column layout */}
          <div className="flex flex-col lg:flex-row gap-8 max-w-7xl mx-auto">
            {/* Left Column - Contact Form */}
            <div className="w-full lg:w-7/12 order-2 lg:order-1">
              <AnimatedElement animation="slide-up" delay={200}>
                <div className="card-enhanced p-8">
                  <h3 className="heading-md mb-8 text-gradient-purple">Send us a Message</h3>
                  <div className="space-y-6">
                    {/* Name Field */}
                    <div>
                      <label htmlFor="name" className="block text-sm font-semibold mb-3 text-white/90">
                        Full Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        placeholder="Enter your full name"
                        className="w-full px-5 py-4 glass-effect-subtle border border-white/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400/50 transition-all duration-300 text-white placeholder-white/50 hover:border-white/30"
                      />
                    </div>

                    {/* Email Field */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-semibold mb-3 text-white/90">
                        Email Address
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        placeholder="<EMAIL>"
                        className="w-full px-5 py-4 glass-effect-subtle border border-white/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400/50 transition-all duration-300 text-white placeholder-white/50 hover:border-white/30"
                      />
                    </div>

                    {/* Subject Field */}
                    <div>
                      <label htmlFor="subject" className="block text-sm font-semibold mb-3 text-white/90">
                        Subject
                      </label>
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        placeholder="What would you like to discuss?"
                        className="w-full px-5 py-4 glass-effect-subtle border border-white/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400/50 transition-all duration-300 text-white placeholder-white/50 hover:border-white/30"
                      />
                    </div>

                    {/* Message Field */}
                    <div>
                      <label htmlFor="message" className="block text-sm font-semibold mb-3 text-white/90">
                        Message
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        rows={6}
                        placeholder="Tell us about your project, ideas, or any questions you have..."
                        className="w-full px-5 py-4 glass-effect-subtle border border-white/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400/50 transition-all duration-300 text-white placeholder-white/50 hover:border-white/30 resize-none"
                      ></textarea>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end pt-4">
                      <button
                        type="submit"
                        className="btn-primary inline-flex items-center group px-8 py-4"
                      >
                        <span>Send Message</span>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </AnimatedElement>
            </div>

            {/* Right Column - Contact Information and Social Links */}
            <div className="w-full lg:w-5/12 order-1 lg:order-2">
              {/* Contact Information */}
              <AnimatedElement animation="slide-left" delay={400} className="mb-8">
                <div className="card-enhanced p-8">
                  <h3 className="heading-md mb-8 text-gradient-cyan">Contact Information</h3>
                  <div className="space-y-8">
                    {/* Email */}
                    <div className="flex items-center group hover:scale-105 transition-transform duration-300">
                      <div className="flex-shrink-0 glass-effect-subtle p-4 rounded-2xl mr-6 flex items-center justify-center group-hover:bg-blue-500/20 transition-colors duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg mb-2 text-white">Email</h4>
                        <p className="text-white/80 font-medium"><EMAIL></p>
                      </div>
                    </div>

                    {/* Location */}
                    <div className="flex items-center group hover:scale-105 transition-transform duration-300">
                      <div className="flex-shrink-0 glass-effect-subtle p-4 rounded-2xl mr-6 flex items-center justify-center group-hover:bg-purple-500/20 transition-colors duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg mb-2 text-white">Location</h4>
                        <p className="text-white/80 font-medium">Delhi, India</p>
                      </div>
                    </div>

                    {/* Response Time */}
                    <div className="flex items-center group hover:scale-105 transition-transform duration-300">
                      <div className="flex-shrink-0 glass-effect-subtle p-4 rounded-2xl mr-6 flex items-center justify-center group-hover:bg-green-500/20 transition-colors duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-semibold text-lg mb-2 text-white">Response Time</h4>
                        <p className="text-white/80 font-medium">Within 24 hours</p>
                      </div>
                    </div>
                  </div>
                </div>
              </AnimatedElement>

              {/* Social Links */}
              <AnimatedElement animation="slide-left" delay={600}>
                <div className="card-enhanced p-8">
                  <h3 className="heading-md mb-8 text-gradient-purple">Connect With Us</h3>
                  <div className="grid grid-cols-2 gap-6">
                    <a
                      href="https://github.com/Xenonesis/Team-Blitz"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex flex-col items-center justify-center p-6 rounded-2xl glass-effect-subtle hover:glass-effect-strong transition-all duration-300 group hover:scale-105 border border-white/10 hover:border-white/20"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-3 text-white group-hover:text-blue-300 transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                      </svg>
                      <span className="text-sm font-semibold text-white group-hover:text-blue-300 transition-colors duration-300">GitHub</span>
                    </a>
                    <a
                      href="https://www.linkedin.com/in/itisaddy/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex flex-col items-center justify-center p-6 rounded-2xl glass-effect-subtle hover:glass-effect-strong transition-all duration-300 group hover:scale-105 border border-white/10 hover:border-white/20"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-3 text-white group-hover:text-blue-400 transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                      </svg>
                      <span className="text-sm font-semibold text-white group-hover:text-blue-400 transition-colors duration-300">LinkedIn</span>
                    </a>
                    <a
                      href="https://iaddy.netlify.app/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex flex-col items-center justify-center p-6 rounded-2xl glass-effect-subtle hover:glass-effect-strong transition-all duration-300 group hover:scale-105 border border-white/10 hover:border-white/20"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-3 text-white group-hover:text-purple-400 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                      </svg>
                      <span className="text-sm font-semibold text-white group-hover:text-purple-400 transition-colors duration-300">Portfolio</span>
                    </a>
                    <a
                      href="mailto:<EMAIL>"
                      className="flex flex-col items-center justify-center p-6 rounded-2xl glass-effect-subtle hover:glass-effect-strong transition-all duration-300 group hover:scale-105 border border-white/10 hover:border-white/20"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mb-3 text-white group-hover:text-green-400 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                      <span className="text-sm font-semibold text-white group-hover:text-green-400 transition-colors duration-300">Email</span>
                    </a>
                  </div>
                </div>
              </AnimatedElement>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div id="faq" className="max-w-5xl mx-auto mt-24 px-6">
          <AnimatedElement animation="fade-in" duration={1000}>
            <h2 className="heading-lg font-bold mb-8 text-center">
              <span className="text-gradient-enhanced">Frequently Asked Questions</span>
            </h2>
            <p className="text-enhanced text-center max-w-4xl mx-auto mb-16">
              Find answers to common questions about Team Blitz and our services. Can&apos;t find what you&apos;re looking for? Feel free to reach out!
            </p>
          </AnimatedElement>

          <FAQ />
        </div>
      </section>

      {/* Enhanced Footer */}
      <Footer variant="simplified" />
    </div>
  );
}
