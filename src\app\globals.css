/* Optimized font loading with preload and fallbacks */

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #0a0a0f;
  --foreground: #ffffff;
  --primary: #3b82f6;
  --primary-dark: #2563eb;
  --primary-light: #60a5fa;
  --secondary: #4f46e5;
  --secondary-light: #6366f1;
  --accent: #8b5cf6;
  --accent-light: #a78bfa;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --surface: #1e1e2e;
  --surface-light: #2a2a3e;
  --border: rgba(255, 255, 255, 0.1);
  --border-light: rgba(255, 255, 255, 0.2);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
  --transition-slow: 0.5s;
  --transition-medium: 0.3s;
  --transition-fast: 0.15s;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);

  /* Theme-aware background effect colors - defaults */
  --bg-effect-1: rgba(59, 130, 246, 0.15);
  --bg-effect-2: rgba(79, 70, 229, 0.12);
  --bg-effect-3: rgba(96, 165, 250, 0.1);
  --bg-effect-4: rgba(147, 197, 253, 0.08);
  --bg-effect-5: rgba(59, 130, 246, 0.06);

  /* Theme-aware particle colors - defaults */
  --particle-color-1: rgba(59, 130, 246, 0.8);
  --particle-color-2: rgba(96, 165, 250, 0.6);
  --particle-color-3: rgba(147, 197, 253, 0.4);

  /* Theme-aware glow colors - defaults */
  --glow-color-1: rgba(59, 130, 246, 0.3);
  --glow-color-2: rgba(79, 70, 229, 0.25);
  --glow-color-3: rgba(96, 165, 250, 0.2);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 25%, #16213e 50%, #0f1419 75%, #1e1e2e 100%);
  color: var(--foreground);
  font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: -0.01em;
  overflow-x: hidden;
  scroll-behavior: smooth;
  position: relative;

  /* Performance optimizations */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  /* GPU acceleration for better performance */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    scroll-behavior: auto;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dynamic Background with Animated Gradients */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, var(--bg-effect-1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, var(--bg-effect-2) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, var(--bg-effect-3) 0%, transparent 50%),
    linear-gradient(135deg,
      rgba(15, 20, 25, 0.8) 0%,
      rgba(26, 26, 46, 0.9) 25%,
      rgba(22, 33, 62, 0.85) 50%,
      rgba(15, 20, 25, 0.9) 75%,
      rgba(30, 30, 46, 0.8) 100%
    );
  background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;
  animation: backgroundShift 20s ease-in-out infinite;
  z-index: -2;
  pointer-events: none;
}

/* Secondary animated layer for more depth */
body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 60% 70%, var(--bg-effect-4) 0%, transparent 60%),
    radial-gradient(circle at 30% 30%, var(--bg-effect-5) 0%, transparent 60%),
    radial-gradient(circle at 70% 10%, var(--glow-color-1) 0%, transparent 60%);
  background-size: 150% 150%, 120% 120%, 180% 180%;
  animation: backgroundFloat 25s ease-in-out infinite reverse;
  z-index: -1;
  pointer-events: none;
}

body.menu-open {
  touch-action: none;
  position: fixed;
  width: 100%;
  height: 100%;
}

/* 3D Card Styles */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

.rounded-inherit {
  border-radius: inherit;
}

/* Improved Animations */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Custom Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes glowPulse {
  0% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 15px rgba(59, 130, 246, 0.6); }
  100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
}

@keyframes navItemHover {
  0% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
  100% { transform: translateY(0); }
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes navItemHover {
  0% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
  100% { transform: translateY(0); }
}

@keyframes glowPulse {
  0% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 15px rgba(59, 130, 246, 0.8); }
  100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes floatSubtle {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
  100% { transform: translateY(0px); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Performance-Optimized Animation Classes */
.animate-fade-in {
  animation: fadeIn 1s ease forwards;
  will-change: opacity;
}

.animate-slide-up {
  animation: slideUp 0.8s ease forwards;
  will-change: transform, opacity;
}

.animate-slide-left {
  animation: slideInLeft 0.8s ease forwards;
  will-change: transform, opacity;
}

.animate-slide-right {
  animation: slideInRight 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  will-change: transform, opacity;
}

.animate-nav-hover {
  animation: navItemHover 0.5s ease;
  will-change: transform;
}

.animate-glow {
  animation: glowPulse 2s infinite;
  will-change: box-shadow;
}

.animate-pulse {
  animation: pulse 2s infinite;
  will-change: transform;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
  will-change: transform;
}

.animate-float-subtle {
  animation: floatSubtle 6s ease-in-out infinite;
  will-change: transform;
}

.animate-spin-slow {
  animation: spin 8s linear infinite;
  will-change: transform;
}

/* Performance utilities */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-up,
  .animate-slide-left,
  .animate-slide-right,
  .animate-nav-hover,
  .animate-glow,
  .animate-pulse,
  .animate-float,
  .animate-float-subtle,
  .animate-spin-slow {
    animation: none !important;
    transition: none !important;
  }

  .gpu-accelerated {
    transform: none;
  }
}

/* Animation Delays */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

/* Menu Item Animation Delays */
.menu-delay-0 {
  animation-delay: 0ms;
}

.menu-delay-1 {
  animation-delay: 100ms;
}

.menu-delay-2 {
  animation-delay: 200ms;
}

.menu-delay-3 {
  animation-delay: 300ms;
}

.menu-delay-4 {
  animation-delay: 400ms;
}

/* Enhanced Utility Classes */
.glass-effect {
  background: rgba(255, 255, 255, 0.08);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-effect-strong {
  background: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.glass-effect-subtle {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(12px);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
}

/* Modern Navbar Styles - Transparent */
.modern-nav-scrolled {
  background: rgba(13, 16, 45, 0.15);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.modern-mobile-menu {
  background: rgba(17, 24, 39, 0.95);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  box-shadow: -5px 0 30px rgba(0, 0, 0, 0.3);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  border-top-left-radius: 16px;
  border-bottom-left-radius: 16px;
}

/* Active Navigation Link */
.nav-active {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.nav-active::before {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -2px;
  transform: translateX(-50%);
  width: 15px;
  height: 3px;
  background: var(--primary);
  border-radius: 3px;
}

.text-gradient {
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-image: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

.text-gradient-enhanced {
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-image: linear-gradient(135deg, #3b82f6, #4f46e5, #8b5cf6, #6366f1);
  background-size: 300% 300%;
  animation: gradientShift 4s ease infinite;
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.3));
}

.text-gradient-purple {
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-image: linear-gradient(135deg, #8b5cf6, #a78bfa, #c084fc);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

.text-gradient-cyan {
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-image: linear-gradient(135deg, #06b6d4, #0891b2, #0e7490);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Background Animation Keyframes */
@keyframes backgroundShift {
  0% {
    background-position: 0% 0%, 100% 100%, 50% 50%, 0% 0%;
    filter: hue-rotate(0deg);
  }
  25% {
    background-position: 100% 0%, 0% 100%, 100% 0%, 25% 25%;
    filter: hue-rotate(90deg);
  }
  50% {
    background-position: 100% 100%, 0% 0%, 0% 100%, 50% 50%;
    filter: hue-rotate(180deg);
  }
  75% {
    background-position: 0% 100%, 100% 0%, 50% 0%, 75% 75%;
    filter: hue-rotate(270deg);
  }
  100% {
    background-position: 0% 0%, 100% 100%, 50% 50%, 0% 0%;
    filter: hue-rotate(360deg);
  }
}

@keyframes backgroundFloat {
  0% {
    background-position: 0% 0%, 50% 50%, 100% 0%;
    transform: scale(1) rotate(0deg);
  }
  33% {
    background-position: 100% 50%, 0% 100%, 50% 50%;
    transform: scale(1.05) rotate(120deg);
  }
  66% {
    background-position: 50% 100%, 100% 0%, 0% 100%;
    transform: scale(0.95) rotate(240deg);
  }
  100% {
    background-position: 0% 0%, 50% 50%, 100% 0%;
    transform: scale(1) rotate(360deg);
  }
}

/* Lightning Effect Animations */
@keyframes lightning {
  0% {
    opacity: 0;
    transform: scale(0.8) rotate(0deg);
  }
  2% {
    opacity: 1;
    transform: scale(1.2) rotate(5deg);
    filter: brightness(2) hue-rotate(0deg);
  }
  4% {
    opacity: 0.8;
    transform: scale(1) rotate(-2deg);
    filter: brightness(1.5) hue-rotate(30deg);
  }
  6% {
    opacity: 1;
    transform: scale(1.1) rotate(3deg);
    filter: brightness(2.5) hue-rotate(60deg);
  }
  8% {
    opacity: 0;
    transform: scale(0.9) rotate(0deg);
    filter: brightness(1) hue-rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) rotate(0deg);
    filter: brightness(1) hue-rotate(0deg);
  }
}

@keyframes lightningGlow {
  0% {
    box-shadow: 0 0 0 rgba(255, 255, 255, 0);
  }
  2% {
    box-shadow:
      0 0 20px rgba(255, 255, 255, 0.8),
      0 0 40px rgba(59, 130, 246, 0.6),
      0 0 60px rgba(139, 92, 246, 0.4);
  }
  4% {
    box-shadow:
      0 0 15px rgba(255, 255, 255, 0.6),
      0 0 30px rgba(59, 130, 246, 0.4),
      0 0 45px rgba(139, 92, 246, 0.3);
  }
  6% {
    box-shadow:
      0 0 25px rgba(255, 255, 255, 0.9),
      0 0 50px rgba(59, 130, 246, 0.7),
      0 0 75px rgba(139, 92, 246, 0.5);
  }
  8% {
    box-shadow: 0 0 0 rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 rgba(255, 255, 255, 0);
  }
}

@keyframes randomLightning1 {
  0% { opacity: 0; }
  1% { opacity: 1; }
  2% { opacity: 0; }
  3% { opacity: 1; }
  4% { opacity: 0; }
  100% { opacity: 0; }
}

@keyframes randomLightning2 {
  0% { opacity: 0; }
  15% { opacity: 0; }
  16% { opacity: 1; }
  17% { opacity: 0; }
  18% { opacity: 1; }
  19% { opacity: 0; }
  100% { opacity: 0; }
}

@keyframes randomLightning3 {
  0% { opacity: 0; }
  35% { opacity: 0; }
  36% { opacity: 1; }
  37% { opacity: 0; }
  100% { opacity: 0; }
}

/* Advanced Background Animations */
@keyframes cosmicDrift {
  0% {
    background-position: 0% 0%, 100% 100%, 50% 50%;
    filter: hue-rotate(0deg) brightness(1);
  }
  20% {
    background-position: 30% 20%, 70% 80%, 80% 30%;
    filter: hue-rotate(72deg) brightness(1.1);
  }
  40% {
    background-position: 60% 40%, 40% 60%, 20% 70%;
    filter: hue-rotate(144deg) brightness(0.9);
  }
  60% {
    background-position: 90% 60%, 10% 40%, 60% 90%;
    filter: hue-rotate(216deg) brightness(1.05);
  }
  80% {
    background-position: 20% 80%, 80% 20%, 40% 10%;
    filter: hue-rotate(288deg) brightness(0.95);
  }
  100% {
    background-position: 0% 0%, 100% 100%, 50% 50%;
    filter: hue-rotate(360deg) brightness(1);
  }
}

@keyframes auroraBorealis {
  0% {
    background-position: 0% 50%, 100% 50%;
    opacity: 0.3;
  }
  25% {
    background-position: 50% 0%, 50% 100%;
    opacity: 0.6;
  }
  50% {
    background-position: 100% 50%, 0% 50%;
    opacity: 0.4;
  }
  75% {
    background-position: 50% 100%, 50% 0%;
    opacity: 0.7;
  }
  100% {
    background-position: 0% 50%, 100% 50%;
    opacity: 0.3;
  }
}

.animate-pulse-subtle {
  animation: pulse 3s infinite;
}

/* Dynamic Lighting Effects */
.dynamic-light-pulse {
  will-change: transform, opacity;
  animation: dynamicLightPulse 4s ease-in-out infinite;
}

.dynamic-light-drift {
  will-change: transform, opacity;
  animation: dynamicLightDrift 6s ease-in-out infinite;
}

.dynamic-light-glow {
  will-change: transform, opacity;
  animation: dynamicLightGlow 5s ease-in-out infinite;
}

@keyframes dynamicLightPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    filter: blur(20px) brightness(1);
  }
  25% {
    transform: translate(-50%, -50%) scale(1.1) translateX(5px);
    filter: blur(18px) brightness(1.2);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05) translateY(-3px);
    filter: blur(22px) brightness(0.9);
  }
  75% {
    transform: translate(-50%, -50%) scale(1.15) translateX(-3px);
    filter: blur(16px) brightness(1.3);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    filter: blur(20px) brightness(1);
  }
}

@keyframes dynamicLightFadeIn {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
    filter: blur(30px);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
    filter: blur(20px);
  }
}

@keyframes dynamicLightFadeOut {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
    filter: blur(20px);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.3);
    filter: blur(40px);
  }
}

@keyframes dynamicLightDrift {
  0% {
    transform: translate(-50%, -50%) scale(1) translateX(0px) translateY(0px);
    filter: blur(20px) brightness(1);
  }
  25% {
    transform: translate(-50%, -50%) scale(1.05) translateX(15px) translateY(-10px);
    filter: blur(18px) brightness(1.1);
  }
  50% {
    transform: translate(-50%, -50%) scale(0.95) translateX(-10px) translateY(15px);
    filter: blur(22px) brightness(0.9);
  }
  75% {
    transform: translate(-50%, -50%) scale(1.1) translateX(-20px) translateY(-5px);
    filter: blur(16px) brightness(1.2);
  }
  100% {
    transform: translate(-50%, -50%) scale(1) translateX(0px) translateY(0px);
    filter: blur(20px) brightness(1);
  }
}

@keyframes dynamicLightGlow {
  0% {
    transform: translate(-50%, -50%) scale(1);
    filter: blur(20px) brightness(1) hue-rotate(0deg);
  }
  20% {
    transform: translate(-50%, -50%) scale(1.2);
    filter: blur(15px) brightness(1.5) hue-rotate(30deg);
  }
  40% {
    transform: translate(-50%, -50%) scale(0.8);
    filter: blur(25px) brightness(0.7) hue-rotate(60deg);
  }
  60% {
    transform: translate(-50%, -50%) scale(1.3);
    filter: blur(12px) brightness(1.8) hue-rotate(90deg);
  }
  80% {
    transform: translate(-50%, -50%) scale(0.9);
    filter: blur(28px) brightness(0.6) hue-rotate(120deg);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    filter: blur(20px) brightness(1) hue-rotate(0deg);
  }
}

/* Lightning Effect Classes */
.lightning-bolt {
  position: absolute;
  width: 2px;
  height: 100px;
  background: linear-gradient(to bottom,
    rgba(255, 255, 255, 0.9) 0%,
    var(--glow-color-1) 50%,
    var(--glow-color-2) 100%
  );
  border-radius: 1px;
  opacity: 0;
  pointer-events: none;
  z-index: 1;
  box-shadow: 0 0 10px var(--glow-color-3);
}

.lightning-bolt-1 {
  top: 10%;
  left: 15%;
  animation: randomLightning1 8s infinite;
  animation-delay: 0s;
}

.lightning-bolt-2 {
  top: 30%;
  right: 20%;
  animation: randomLightning2 12s infinite;
  animation-delay: 3s;
}

.lightning-bolt-3 {
  top: 60%;
  left: 70%;
  animation: randomLightning3 15s infinite;
  animation-delay: 6s;
}

.lightning-bolt-4 {
  top: 80%;
  right: 40%;
  animation: randomLightning1 10s infinite;
  animation-delay: 9s;
}

.lightning-bolt-5 {
  top: 20%;
  left: 50%;
  animation: randomLightning2 14s infinite;
  animation-delay: 12s;
}

/* Dynamic Background Overlay */
.dynamic-bg-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 25% 25%, var(--bg-effect-1) 0%, transparent 60%),
    radial-gradient(circle at 75% 75%, var(--bg-effect-2) 0%, transparent 60%),
    radial-gradient(circle at 50% 50%, var(--bg-effect-3) 0%, transparent 60%),
    radial-gradient(circle at 10% 90%, var(--glow-color-2) 0%, transparent 50%),
    radial-gradient(circle at 90% 10%, var(--glow-color-3) 0%, transparent 50%);
  background-size: 200% 200%, 150% 150%, 300% 300%, 250% 250%, 180% 180%;
  animation: backgroundShift 30s ease-in-out infinite;
  z-index: -3;
  pointer-events: none;
}

/* Additional Dynamic Layers */
.dynamic-bg-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    conic-gradient(from 0deg at 30% 70%, var(--glow-color-1) 0deg, transparent 60deg, var(--glow-color-2) 120deg, transparent 180deg),
    conic-gradient(from 180deg at 70% 30%, var(--glow-color-3) 0deg, transparent 90deg, var(--bg-effect-4) 180deg, transparent 270deg);
  background-size: 400% 400%, 350% 350%;
  animation: backgroundFloat 40s ease-in-out infinite reverse;
  opacity: 0.7;
}

.dynamic-bg-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(45deg, var(--bg-effect-5) 0%, transparent 25%, var(--glow-color-1) 50%, transparent 75%, var(--bg-effect-3) 100%),
    linear-gradient(-45deg, var(--glow-color-2) 0%, transparent 30%, var(--bg-effect-4) 60%, transparent 100%);
  background-size: 600% 600%, 500% 500%;
  animation: backgroundShift 50s ease-in-out infinite;
  opacity: 0.8;
}

.nav-link-enhanced {
  position: relative;
  transition: all 0.3s ease;
}

.nav-link-enhanced:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}

.nav-link-enhanced::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, #3b82f6, #4f46e5);
  transition: width 0.3s ease;
}

.nav-link-enhanced:hover::after {
  width: 100%;
}

/* Hamburger Menu */
/* Enhanced Hamburger Menu */
.hamburger-menu {
  width: 22px;
  height: 16px;
  position: relative;
  transform: rotate(0deg);
  transition: .4s ease-in-out;
  cursor: pointer;
}

.hamburger-menu span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: white;
  border-radius: 4px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: .25s ease-in-out;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.hamburger-menu span:nth-child(1) {
  top: 0px;
  width: 100%;
  transform-origin: left center;
}

.hamburger-menu span:nth-child(2) {
  top: 7px;
  width: 85%;
  transform-origin: left center;
}

.hamburger-menu span:nth-child(3) {
  top: 14px;
  width: 70%;
  transform-origin: left center;
}

.hamburger-menu.open span:nth-child(1) {
  top: 0;
  left: 2px;
  transform: rotate(45deg);
  width: 100%;
}

.hamburger-menu.open span:nth-child(2) {
  opacity: 0;
  width: 0;
}

.hamburger-menu.open span:nth-child(3) {
  top: 14px;
  left: 2px;
  transform: rotate(-45deg);
  width: 100%;
}

/* Navigation Link Hover Effect */
.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--primary);
  transition: width 0.3s ease;
  border-radius: 2px;
}

.nav-link:hover::after {
  width: 100%;
}

.hover-scale {
  transition: transform var(--transition-medium) ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #1e1e24;
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Enhanced Button Styles */
.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, var(--primary-light), var(--secondary-light));
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

/* Enhanced Card Styles */
.card-enhanced {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.card-enhanced:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Enhanced Footer Styles */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

/* Footer Dynamic Background Effects */
:root {
  --footer-bg-primary: rgba(59, 130, 246, 0.15);
  --footer-bg-secondary: rgba(79, 70, 229, 0.12);
  --footer-bg-accent: rgba(96, 165, 250, 0.1);
  --footer-glow: rgba(59, 130, 246, 0.3);
}

/* Slow spin animation for footer decorative elements - optimized for performance */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
  will-change: transform;
}

/* Typography Enhancements */
.heading-xl {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.heading-lg {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.015em;
}

.heading-md {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.text-enhanced {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--text-secondary);
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .glass-effect,
  .glass-effect-strong,
  .glass-effect-subtle {
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
  }

  .card-enhanced {
    padding: 1rem;
  }

  .btn-primary,
  .btn-secondary {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
  }
}
