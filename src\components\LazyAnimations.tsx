"use client";

import { lazy, Suspense, useEffect, useState } from 'react';

// Lazy load heavy animation components
const ParticleBackground = lazy(() =>
  import('@/utils/animations').then(module => ({ default: module.ParticleBackground }))
);

const LightningBackground = lazy(() =>
  import('@/utils/animations').then(module => ({ default: module.LightningBackground }))
);

const DynamicLighting = lazy(() =>
  import('@/utils/animations').then(module => ({ default: module.DynamicLighting }))
);

// Performance-aware animation loader
interface LazyAnimationProps {
  enableParticles?: boolean;
  enableLightning?: boolean;
  enableDynamicLighting?: boolean;
  priority?: 'low' | 'medium' | 'high';
}

export function LazyAnimationLoader({
  enableParticles = true,
  enableLightning = true,
  enableDynamicLighting = true,
  priority = 'medium'
}: LazyAnimationProps) {
  const [shouldLoad, setShouldLoad] = useState(false);

  useEffect(() => {
    // Check if user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      return;
    }

    // Check device capabilities
    const isLowEnd = navigator.hardwareConcurrency <= 4 ||
                    /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    // Check connection speed
    const connection = (navigator as Navigator & { connection?: { effectiveType?: string } }).connection;
    const isSlowConnection = connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');

    if (isLowEnd || isSlowConnection) {
      // Only load on high priority for low-end devices
      if (priority === 'high') {
        setShouldLoad(true);
      }
      return;
    }

    // Use Intersection Observer to load when in viewport
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setShouldLoad(true);
            // Delay loading based on priority
            const delay = priority === 'high' ? 0 : priority === 'medium' ? 100 : 500;
            setTimeout(() => setShouldLoad(true), delay);
            observer.disconnect();
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(document.body);

    return () => observer.disconnect();
  }, [priority]);

  if (!shouldLoad) {
    return null;
  }

  return (
    <Suspense fallback={null}>
      {enableParticles && (
        <ParticleBackground />
      )}
      {enableLightning && (
        <LightningBackground />
      )}
      {enableDynamicLighting && (
        <DynamicLighting />
      )}
    </Suspense>
  );
}

// Optimized scroll progress component
export function OptimizedScrollProgress() {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    let ticking = false;

    const updateScrollProgress = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (scrollTop / docHeight) * 100;
      setScrollProgress(progress);
      ticking = false;
    };

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateScrollProgress);
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="fixed top-0 left-0 w-full h-1 bg-transparent z-50 pointer-events-none">
      <div
        className="h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-transform duration-150 ease-out origin-left"
        style={{ transform: `scaleX(${scrollProgress / 100})` }}
      />
    </div>
  );
}

// Performance monitoring hook
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = useState({
    fps: 60,
    memoryUsage: 0,
    isLowPerformance: false
  });

  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));

        // Get memory usage if available
        const memory = (performance as Performance & { memory?: { usedJSHeapSize: number; jsHeapSizeLimit: number } }).memory;
        const memoryUsage = memory ? memory.usedJSHeapSize / memory.jsHeapSizeLimit : 0;

        setMetrics({
          fps,
          memoryUsage,
          isLowPerformance: fps < 30 || memoryUsage > 0.8
        });

        frameCount = 0;
        lastTime = currentTime;
      }

      animationId = requestAnimationFrame(measureFPS);
    };

    animationId = requestAnimationFrame(measureFPS);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  return metrics;
}

export default LazyAnimationLoader;
