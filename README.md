# 🚀 Team Blitz v5.5

<div align="center">
  <img src="public/logo.png" alt="Team Blitz Logo" width="200" />
  <p><em>Innovate. Create. Collaborate. - Now with Enhanced Visuals!</em></p>
  <p><strong>Version 5.5</strong> - May 2025</p>
</div>

<div align="center">
  <img src="public/logo.png" alt="Team Blitz Logo" width="200" />
  <p><em>Innovate. Create. Collaborate.</em></p>
</div>

A modern, responsive landing page for Team Blitz, showcasing our talented team members and innovative projects. Built with cutting-edge web technologies for optimal performance and user experience.

[![Next.js](https://img.shields.io/badge/Next.js-15.3.2-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19.0.0-blue?style=for-the-badge&logo=react)](https://react.dev/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-4.0.0-38B2AC?style=for-the-badge&logo=tailwind-css)](https://tailwindcss.com/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0.0-3178C6?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)

## ✨ What's New in v5.0

- **Custom SVG Avatars** - Unique, handcrafted avatars for each team member
- **Enhanced UI/UX** - Improved animations and transitions
- **Project Showcase** - Featured project: SwiftDrop highlighted
- **Performance Optimizations** - Faster load times and smoother interactions
- **Responsive Design** - Perfect on all devices

## ✨ Features

- **Responsive Design** - Optimized for all devices from mobile to desktop
- **Modern UI** - Sleek glass-effect components with smooth animations
- **Team Profiles** - Detailed team member information with social links
- **Project Showcase** - Interactive gallery of our latest projects
- **Dark/Light Mode** - Toggle between dark and light themes
- **Performance Optimized** - Fast loading times and smooth interactions

## 🌟 Featured Project

### SwiftDrop
A cutting-edge file sharing application with end-to-end encryption and lightning-fast transfers.

[![SwiftDrop Demo](public/swiftdrop-preview.jpg)](https://swiftdrop.example.com)

## 👥 Our Team

<table>
  <tr>
    <td align="center">
      <img src="https://github.com/Xenonesis.png" width="100px" alt="Aditya Kumar Tiwari"/><br />
      <b>Aditya Kumar Tiwari</b><br />
      Team Leader & Developer<br />
      <a href="https://www.linkedin.com/in/itisaddy/">LinkedIn</a> •
      <a href="https://github.com/Xenonesis">GitHub</a> •
      <a href="https://iaddy.netlify.app/">Portfolio</a>
    </td>
    <td align="center">
      <img src="https://github.com/SwatiMishra01.png" width="100px" alt="Swati Mishra"/><br />
      <b>Swati Mishra</b><br />
      Developer<br />
      <a href="https://www.linkedin.com/in/swati-mishra-8a5a18259">LinkedIn</a> •
      <a href="https://github.com/SwatiMishra01">GitHub</a>
    </td>
  </tr>
  <tr>
    <td align="center">
      <img src="https://github.com/Amaayu.png" width="100px" alt="Aayush Tonk"/><br />
      <b>Aayush Tonk</b><br />
      Backend Engineer<br />
      <a href="https://www.linkedin.com/in/aayush-tonk/">LinkedIn</a> •
      <a href="https://github.com/Amaayu">GitHub</a>
    </td>
    <td align="center">
      <img src="https://github.com/Mohammad-Ehshan.png" width="100px" alt="Mohammad Ehshan"/><br />
      <b>Mohammad Ehshan</b><br />
      Frontend Developer<br />
      <a href="https://www.linkedin.com/in/mohammad-ehshan-4362a0298/">LinkedIn</a> •
      <a href="https://github.com/Mohammad-Ehshan">GitHub</a>
    </td>
  </tr>
</table>

## 🛠️ Technologies Used

- **Frontend**: Next.js, React, Tailwind CSS
- **Language**: TypeScript
- **Deployment**: Vercel
- **Version Control**: Git & GitHub

## 🚀 Getting Started

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

### Prerequisites

- Node.js 18.17.0 or later
- npm, yarn, pnpm, or bun

### Installation

```bash
# Clone the repository
git clone https://github.com/Xenonesis/Team-Blitz.git
cd Team-Blitz/hackathon-team-landing

# Install dependencies
npm install
# or
yarn install
# or
pnpm install
```

### Development

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## 📝 Version History

- **5.5** - Version update to 5.5
- **5.0** - Custom SVG avatars, enhanced UI, SwiftDrop showcase
- **4.0** - Dark mode, performance improvements
- **3.0** - Team section redesign, animations
- **2.0** - Responsive layout, mobile optimization
- **1.0** - Initial release with basic structure
