<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub Update Instructions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #0366d6;
            border-bottom: 2px solid #eaecef;
            padding-bottom: 10px;
        }
        h2 {
            color: #24292e;
            margin-top: 30px;
        }
        code {
            background-color: #f6f8fa;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f6f8fa;
            padding: 16px;
            border-radius: 6px;
            overflow-x: auto;
        }
        .note {
            background-color: #fffbdd;
            border-left: 4px solid #ffd33d;
            padding: 15px;
            margin: 20px 0;
        }
        .step {
            margin-bottom: 20px;
            padding-left: 20px;
            border-left: 4px solid #0366d6;
        }
    </style>
</head>
<body>
    <h1>GitHub Update Instructions</h1>
    
    <p>This document provides instructions on how to update the Team Blitz repository on GitHub with the latest changes (version 0.2.0).</p>
    
    <div class="note">
        <strong>Note:</strong> These instructions assume you have Git installed on your computer and have access to the Team Blitz GitHub repository.
    </div>
    
    <h2>What Has Been Updated</h2>
    <p>The following files have been updated:</p>
    <ul>
        <li><code>README.md</code> - Improved design with badges, team member profiles, and better formatting</li>
        <li><code>package.json</code> - Version updated from 0.1.0 to 0.2.0</li>
    </ul>
    
    <h2>Step-by-Step Instructions</h2>
    
    <div class="step">
        <h3>Step 1: Open a Terminal or Command Prompt</h3>
        <p>Open a terminal or command prompt and navigate to the project directory:</p>
        <pre><code>cd path/to/Team-Blitz/hackathon-team-landing</code></pre>
    </div>
    
    <div class="step">
        <h3>Step 2: Initialize Git Repository (if not already done)</h3>
        <p>If the project is not already a Git repository, initialize it:</p>
        <pre><code>git init</code></pre>
    </div>
    
    <div class="step">
        <h3>Step 3: Configure Git (if not already done)</h3>
        <p>Set your Git username and email if not already configured:</p>
        <pre><code>git config user.name "Aditya Kumar Tiwari"
git config user.email "<EMAIL>"</code></pre>
    </div>
    
    <div class="step">
        <h3>Step 4: Add Files to Git</h3>
        <p>Add the modified files to Git:</p>
        <pre><code>git add README.md package.json</code></pre>
    </div>
    
    <div class="step">
        <h3>Step 5: Commit Changes</h3>
        <p>Commit the changes with a descriptive message:</p>
        <pre><code>git commit -m "Update to version 0.2.0 with improved README design"</code></pre>
    </div>
    
    <div class="step">
        <h3>Step 6: Add Remote Repository (if not already done)</h3>
        <p>If you haven't already connected to the GitHub repository, add it as a remote:</p>
        <pre><code>git remote add origin https://github.com/Xenonesis/Team-Blitz.git</code></pre>
    </div>
    
    <div class="step">
        <h3>Step 7: Push Changes to GitHub</h3>
        <p>Push the changes to GitHub:</p>
        <pre><code>git push -u origin master</code></pre>
        <p>If you're using a different branch, replace "master" with your branch name.</p>
    </div>
    
    <div class="note">
        <strong>Authentication:</strong> You may be prompted to enter your GitHub username and password or a personal access token. If you have two-factor authentication enabled, you'll need to use a personal access token instead of your password.
    </div>
    
    <h2>Verifying the Update</h2>
    <p>After pushing the changes, visit the GitHub repository at <a href="https://github.com/Xenonesis/Team-Blitz">https://github.com/Xenonesis/Team-Blitz</a> to verify that the changes have been successfully uploaded.</p>
    
    <h2>Troubleshooting</h2>
    <ul>
        <li>If you encounter permission issues, make sure you have write access to the repository.</li>
        <li>If you get an error about the remote repository not existing, double-check the repository URL.</li>
        <li>If you have conflicts, you may need to pull the latest changes first with <code>git pull origin master</code>.</li>
    </ul>
</body>
</html>
