import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import "./responsive-fixes.css";

// Optimized font loading with preload and subset optimization
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  weight: ["400", "500", "600", "700", "800"],
  display: "swap",
  preload: true,
  fallback: [
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'sans-serif'
  ],
});

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  weight: ["400", "500", "600", "700", "800"],
  display: "swap",
  preload: false, // Load on demand
  fallback: [
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    '<PERSON><PERSON>',
    'sans-serif'
  ],
});

export const metadata: Metadata = {
  title: "Team Blitz | Aditya's Hackathon Team",
  description: "Meet Team Blitz - <PERSON>itya <PERSON>'s innovative hackathon team building amazing solutions",
  keywords: ["hackathon", "team blitz", "aditya kumar tiwari", "innovation", "technology", "development"],
  authors: [{ name: "Aditya <PERSON> Tiwari" }],
  creator: "Team Blitz",
  publisher: "Team Blitz",
  manifest: '/manifest.json',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/favicon.svg', type: 'image/svg+xml' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180' },
    ],
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://teamblitz.dev',
    title: "Team Blitz | Aditya's Hackathon Team",
    description: "Meet Team Blitz - Aditya Kumar Tiwari's innovative hackathon team building amazing solutions",
    siteName: 'Team Blitz',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Team Blitz | Aditya's Hackathon Team",
    description: "Meet Team Blitz - Aditya Kumar Tiwari's innovative hackathon team building amazing solutions",
  },
};

export const viewport = {
  themeColor: '#3b82f6',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        {/* Viewport meta tag for responsive design */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
        
        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />

        {/* Preconnect to critical origins */}
        <link rel="preconnect" href="https://fonts.googleapis.com" crossOrigin="" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />

        {/* Google Fonts will handle font preloading automatically */}

        {/* Performance hints */}
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      </head>
      <body className={`${inter.variable} ${poppins.variable} font-sans antialiased overflow-hidden h-screen`}>
        <div className="h-full overflow-y-auto">
          {children}
        </div>
      </body>
    </html>
  );
}
