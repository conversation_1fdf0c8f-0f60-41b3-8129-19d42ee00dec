"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface FooterProps {
  variant?: 'full' | 'simplified';
  className?: string;
}

export default function Footer({ variant = 'full', className = '' }: FooterProps) {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);
  const [currentTheme, setCurrentTheme] = useState('blue');

  // Ensure component is mounted before rendering
  useEffect(() => {
    setMounted(true);
  }, []);

  // Dynamic theme based on current page
  useEffect(() => {
    if (!mounted) return;

    const pageThemes: Record<string, string> = {
      '/': 'blue',
      '/projects': 'purple',
      '/contact': 'cyan',
    };

    const theme = pageThemes[pathname] || 'blue';
    setCurrentTheme(theme);

    // Update CSS custom properties for dynamic theming
    const root = document.documentElement;
    const themeColors = {
      blue: {
        primary: 'rgba(59, 130, 246, 0.15)',
        secondary: 'rgba(79, 70, 229, 0.12)',
        accent: 'rgba(96, 165, 250, 0.1)',
        glow: 'rgba(59, 130, 246, 0.3)',
      },
      purple: {
        primary: 'rgba(139, 92, 246, 0.15)',
        secondary: 'rgba(124, 58, 237, 0.12)',
        accent: 'rgba(168, 85, 247, 0.1)',
        glow: 'rgba(139, 92, 246, 0.3)',
      },
      cyan: {
        primary: 'rgba(6, 182, 212, 0.15)',
        secondary: 'rgba(8, 145, 178, 0.12)',
        accent: 'rgba(34, 211, 238, 0.1)',
        glow: 'rgba(6, 182, 212, 0.3)',
      },
    };

    const colors = themeColors[theme as keyof typeof themeColors];
    if (colors) {
      root.style.setProperty('--footer-bg-primary', colors.primary);
      root.style.setProperty('--footer-bg-secondary', colors.secondary);
      root.style.setProperty('--footer-bg-accent', colors.accent);
      root.style.setProperty('--footer-glow', colors.glow);
    }
  }, [pathname, mounted]);

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  const themeGradients = {
    blue: 'from-blue-950 via-blue-900/80 to-black',
    purple: 'from-purple-950 via-purple-900/80 to-black',
    cyan: 'from-cyan-950 via-cyan-900/80 to-black',
  };

  const themeAccents = {
    blue: 'from-blue-500 via-indigo-500 to-purple-500',
    purple: 'from-purple-500 via-violet-500 to-indigo-500',
    cyan: 'from-cyan-500 via-teal-500 to-blue-500',
  };

  const currentGradient = themeGradients[currentTheme as keyof typeof themeGradients];
  const currentAccent = themeAccents[currentTheme as keyof typeof themeAccents];

  if (variant === 'simplified') {
    return (
      <footer className={`relative overflow-hidden transition-all duration-1000 ${className}`}>
        {/* Dynamic Background with Enhanced Gradients */}
        <div className={`bg-gradient-to-b ${currentGradient} py-8 relative`}>
          {/* Animated Top Border */}
          <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${currentAccent} animate-pulse`}></div>

          {/* Dynamic Background Effects */}
          <div className="absolute inset-0 opacity-30">
            <div className="absolute top-0 left-1/4 w-32 h-32 rounded-full bg-gradient-radial from-white/10 to-transparent animate-pulse"></div>
            <div className="absolute bottom-0 right-1/4 w-24 h-24 rounded-full bg-gradient-radial from-white/5 to-transparent animate-pulse" style={{ animationDelay: '1s' }}></div>
          </div>

          <div className="container mx-auto px-6 relative z-10">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm mb-4 md:mb-0 transition-colors duration-300 hover:text-gray-300">
                © {new Date().getFullYear()} Team Blitz. All rights reserved.
              </p>
              <div className="flex space-x-6">
                <button
                  onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                  className="text-gray-400 hover:text-blue-300 text-sm transition-all duration-300 hover:scale-105"
                >
                  Back to Top
                </button>
                {pathname === '/contact' && (
                  <a href="#faq" className="text-gray-400 hover:text-blue-300 text-sm transition-all duration-300 hover:scale-105">
                    FAQ
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </footer>
    );
  }

  return (
    <footer className={`relative overflow-hidden transition-all duration-1000 ${className}`}>
      {/* Enhanced Dynamic Background */}
      <div className={`bg-gradient-to-b ${currentGradient} py-16 relative`}>
        {/* Animated Decorative Elements */}
        <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${currentAccent} animate-pulse`}></div>
        <div className="absolute -top-40 -left-40 w-80 h-80 rounded-full opacity-20 animate-spin-slow" style={{ background: `var(--footer-bg-primary)` }}></div>
        <div className="absolute -bottom-40 -right-40 w-80 h-80 rounded-full opacity-20 animate-spin-slow" style={{ background: `var(--footer-bg-secondary)`, animationDirection: 'reverse' }}></div>

        {/* Dynamic Lighting Integration */}
        <div className="absolute inset-0 opacity-40">
          <div className="absolute top-1/4 left-1/3 w-48 h-48 rounded-full bg-gradient-radial from-white/10 to-transparent animate-pulse"></div>
          <div className="absolute bottom-1/3 right-1/4 w-32 h-32 rounded-full bg-gradient-radial from-white/8 to-transparent animate-pulse" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-2/3 left-1/6 w-24 h-24 rounded-full bg-gradient-radial from-white/6 to-transparent animate-pulse" style={{ animationDelay: '4s' }}></div>
        </div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12 mb-12">
            {/* Logo and Description */}
            <div className="md:col-span-2">
              <div className="flex items-center mb-6 group">
                <div className="text-3xl font-bold text-gradient transition-all duration-300 group-hover:scale-105">Team Blitz</div>
                <div className="ml-2 text-2xl transition-transform duration-300 group-hover:rotate-12">⚡</div>
              </div>
              <p className="text-gray-300 mb-6 max-w-md leading-relaxed transition-colors duration-300 hover:text-gray-200">
                Led by Aditya Kumar Tiwari, we&apos;re a team of passionate developers, designers, and innovators who come together to create amazing solutions during hackathons.
              </p>
              <div className="flex space-x-4">
                <SocialLink href="#" icon="twitter" label="Twitter" />
                <SocialLink href="https://github.com/Xenonesis" icon="github" label="GitHub" />
                <SocialLink href="https://www.linkedin.com/in/itisaddy/" icon="linkedin" label="LinkedIn" />
                <SocialLink href="https://iaddy.netlify.app/" icon="portfolio" label="Portfolio" />
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-bold mb-6 text-blue-300 transition-colors duration-300 hover:text-blue-200">Quick Links</h3>
              <ul className="space-y-4">
                <FooterLink href={pathname === '/' ? '#about' : '/#about'} text="About Us" />
                <FooterLink href={pathname === '/' ? '#team' : '/#team'} text="Our Team" />
                <FooterLink href={pathname === '/' ? '#projects' : '/#projects'} text="Projects" />
                <FooterLink href="/contact" text="Contact" />
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h3 className="text-lg font-bold mb-6 text-blue-300 transition-colors duration-300 hover:text-blue-200">Contact Info</h3>
              <ul className="space-y-4">
                <ContactItem
                  icon="email"
                  text="<EMAIL>"
                  href="mailto:<EMAIL>"
                />
                <ContactItem
                  icon="location"
                  text="Delhi, India"
                />
              </ul>
            </div>
          </div>

          {/* Enhanced Divider */}
          <div className={`h-px bg-gradient-to-r from-transparent via-blue-500/50 to-transparent my-8 transition-all duration-500 hover:via-blue-400/70`}></div>

          {/* Copyright Section */}
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm mb-4 md:mb-0 transition-colors duration-300 hover:text-gray-300">
              © {new Date().getFullYear()} Team Blitz. All rights reserved.
            </div>
            <div className="text-gray-400 text-sm flex items-center space-x-4">
              <a href="#" className="hover:text-blue-300 transition-all duration-300 hover:scale-105">Privacy Policy</a>
              <span className="text-gray-600">•</span>
              <a href="#" className="hover:text-blue-300 transition-all duration-300 hover:scale-105">Terms of Service</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

// Helper Components
interface SocialLinkProps {
  href: string;
  icon: string;
  label: string;
}

function SocialLink({ href, icon, label }: SocialLinkProps) {
  const getIcon = () => {
    switch (icon) {
      case 'twitter':
        return (
          <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
          </svg>
        );
      case 'github':
        return (
          <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
            <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
          </svg>
        );
      case 'linkedin':
        return (
          <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
            <path fillRule="evenodd" d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" clipRule="evenodd" />
          </svg>
        );
      case 'portfolio':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <a
      href={href}
      target={href.startsWith('http') ? '_blank' : undefined}
      rel={href.startsWith('http') ? 'noopener noreferrer' : undefined}
      className="glass-effect-subtle hover:glass-effect p-3 rounded-full transition-all duration-300 hover:scale-110 hover:-translate-y-1 group"
      aria-label={label}
      title={label}
    >
      <div className="transition-colors duration-300 group-hover:text-blue-300">
        {getIcon()}
      </div>
    </a>
  );
}

interface FooterLinkProps {
  href: string;
  text: string;
}

function FooterLink({ href, text }: FooterLinkProps) {
  const isExternal = href.startsWith('http');
  const isAnchor = href.startsWith('#') || href.includes('#');

  if (isAnchor && !isExternal) {
    return (
      <li>
        <a
          href={href}
          className="text-gray-300 hover:text-blue-300 transition-all duration-300 hover:translate-x-1 inline-block"
        >
          {text}
        </a>
      </li>
    );
  }

  return (
    <li>
      <Link
        href={href}
        className="text-gray-300 hover:text-blue-300 transition-all duration-300 hover:translate-x-1 inline-block"
      >
        {text}
      </Link>
    </li>
  );
}

interface ContactItemProps {
  icon: string;
  text: string;
  href?: string;
}

function ContactItem({ icon, text, href }: ContactItemProps) {
  const getIcon = () => {
    switch (icon) {
      case 'email':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-blue-400 transition-colors duration-300 group-hover:text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        );
      case 'location':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 text-blue-400 transition-colors duration-300 group-hover:text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const content = (
    <li className="flex items-start group transition-all duration-300 hover:translate-x-1">
      {getIcon()}
      <span className="text-gray-300 transition-colors duration-300 group-hover:text-gray-200">{text}</span>
    </li>
  );

  if (href) {
    return (
      <a href={href} className="block">
        {content}
      </a>
    );
  }

  return content;
}
